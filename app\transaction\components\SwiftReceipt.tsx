'use client';

import React from 'react';

const SwiftReceipt = () => {
  const transactionData = {
    amount: '$950.00',
    currency: 'USD',
    sender: {
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      bank: 'Canadian Bank',
      account: 'CAD-****-4567'
    },
    receiver: {
      name: '<PERSON>',
      iban: '**********************',
      bank: 'Deutsche Bank',
      bic: 'DEUTDEFF'
    },
    transaction: {
      reference: 'FT24010950001',
      date: new Date().toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      time: new Date().toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        timeZoneName: 'short'
      }),
      status: 'COMPLETED',
      swiftCode: 'MT103',
      processingTime: '2-3 business days'
    }
  };

  const currentDateTime = new Date().toISOString();

  return (
    <div className="max-w-4xl mx-auto bg-white shadow-2xl rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900 to-blue-700 text-white p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">SWIFT Wire Transfer Receipt</h1>
            <p className="text-blue-100 mt-1">International Money Transfer Confirmation</p>
          </div>
          <div className="text-right">
            <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
              {transactionData.transaction.status}
            </div>
          </div>
        </div>
      </div>

      {/* Transaction Summary */}
      <div className="p-6 border-b border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <p className="text-gray-600 text-sm">Transfer Amount</p>
            <p className="text-3xl font-bold text-green-600">{transactionData.amount}</p>
            <p className="text-gray-500 text-sm">{transactionData.currency}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-600 text-sm">Transaction Reference</p>
            <p className="text-xl font-mono font-semibold">{transactionData.transaction.reference}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-600 text-sm">Processing Time</p>
            <p className="text-lg font-semibold">{transactionData.transaction.processingTime}</p>
          </div>
        </div>
      </div>

      {/* Transaction Details */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Sender Information */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-2">S</span>
              Sender Information
            </h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-semibold">{transactionData.sender.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Email</p>
                <p className="font-mono text-sm">{transactionData.sender.email}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Bank</p>
                <p className="font-semibold">{transactionData.sender.bank}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Account</p>
                <p className="font-mono text-sm">{transactionData.sender.account}</p>
              </div>
            </div>
          </div>

          {/* Receiver Information */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-2">R</span>
              Receiver Information
            </h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-600">Name</p>
                <p className="font-semibold">{transactionData.receiver.name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">IBAN</p>
                <p className="font-mono text-sm">{transactionData.receiver.iban}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Bank</p>
                <p className="font-semibold">{transactionData.receiver.bank}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">BIC/SWIFT</p>
                <p className="font-mono text-sm">{transactionData.receiver.bic}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Transaction Timeline */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Transaction Timeline</h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="bg-green-500 rounded-full w-3 h-3 mr-4"></div>
              <div className="flex-1">
                <p className="font-semibold">Transfer Initiated</p>
                <p className="text-sm text-gray-600">{transactionData.transaction.date} at {transactionData.transaction.time}</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="bg-green-500 rounded-full w-3 h-3 mr-4"></div>
              <div className="flex-1">
                <p className="font-semibold">SWIFT Message Sent</p>
                <p className="text-sm text-gray-600">Message Type: {transactionData.transaction.swiftCode}</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="bg-green-500 rounded-full w-3 h-3 mr-4"></div>
              <div className="flex-1">
                <p className="font-semibold">Transfer Completed</p>
                <p className="text-sm text-gray-600">Funds successfully transferred</p>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h4 className="font-semibold text-yellow-800 mb-2">Important Notice</h4>
            <p className="text-sm text-yellow-700">
              This is an official SWIFT transfer confirmation. Please keep this receipt for your records.
              The transfer may take 2-3 business days to reflect in the recipient's account.
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">Transaction Details</h4>
            <div className="text-sm space-y-1">
              <p><span className="text-gray-600">Date:</span> {transactionData.transaction.date}</p>
              <p><span className="text-gray-600">Time:</span> {transactionData.transaction.time}</p>
              <p><span className="text-gray-600">Reference:</span> {transactionData.transaction.reference}</p>
              <p><span className="text-gray-600">SWIFT Code:</span> {transactionData.transaction.swiftCode}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-100 p-6 border-t border-gray-200">
        <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
          <p>© 2024 International Banking Services. All rights reserved.</p>
          <p>Generated on: {currentDateTime}</p>
        </div>
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            This document is computer generated and does not require a signature.
            For inquiries, please contact your bank's customer service.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SwiftReceipt;
